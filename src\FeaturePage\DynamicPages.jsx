import React, { useState, useRef } from "react";
import {
  Card,
  Button,
  Form,
  Input,
  Select,
  Space,
  Row,
  Col,
  Typography,
  message,
  Tooltip,
  Divider,
} from "antd";
import {
  Download,
  Upload,
  Plus,
  Minus,
  Info,
  Settings,
  Eye,
  MapPin,
  Briefcase,
  FileText,
  Layers,
} from "lucide-react";
import TabList from "../components/common/TabList";
import DynamicTab from "./components/DynamicTab";

const { Title, Text } = Typography;
const { Option } = Select;

const DynamicPages = () => {
  const [previewMode, setPreviewMode] = useState("dynamic_pages");

  const tabList = {
    dynamic_pages: {
      key: "dynamic_pages",
      label: "Dynamic Pages",
      icon: <Settings className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: <DynamicTab />,
    },
    branding: {
      key: "branding",
      label: "Branding & Content Preview",
      icon: <Eye className="tw-w-4 tw-h-4 tw-mr-2" />,
      content: <>dfd</>,
    },
  };

  return (
    <div className="tw-min-h-screen tw-bg-gray-50 tw-p-6">
      <div className="tw-max-w-7xl tw-mx-auto">
        {/* Header */}
        <div className="tw-mb-6">
          <Title level={2} className="!tw-mb-2 !tw-text-gray-900">
            Dream Builder v.0.0
          </Title>
          <Text type="secondary" className="tw-text-base">
            Configure your website settings and content
          </Text>
        </div>

        <div>
          {/* Tab Navigation using project's TabList */}
          <TabList
            tabContents={tabList}
            setPreviewMode={setPreviewMode}
            previewMode={previewMode}
          />
          {tabList[previewMode]?.content ? (
            tabList[previewMode]?.content
          ) : (
            <></>
          )}
        </div>
      </div>
    </div>
  );
};

export default DynamicPages;
