import React, { useMemo, useState, useEffect } from "react";

/**
 * React Template Preview Renderer
 * - Fills placeholders from Page Content -> Template Content -> Global Content (contentJSON)
 * - Replaces ${...} variables anywhere in HTML/CSS/JS, incl. nested like ${${slug}_state}
 * - Handles dynamic pages with slugs (e.g., city_page)
 * - Handles repeaters using {{slotName}} inside container component HTML
 * - Renders a simple preview UI with a sidebar of pages and a canvas
 *
 * Props:
 *   templateData: the full template JSON (as in user's example)
 *   dynamicData: dynamic config (e.g., city_page -> sections -> [ { name, slug } ])
 */

/***********************
 * Utilities
 ***********************/
function deepGet(obj, pathParts) {
  if (!obj) return undefined;
  let cur = obj;
  for (const p of pathParts) {
    if (cur && typeof cur === "object" && p in cur) cur = cur[p];
    else return undefined;
  }
  return cur;
}

function mergeShallow(...objs) {
  return objs.reduce((acc, o) => Object.assign(acc, o || {}), {});
}

function replaceSlots(str, slots) {
  if (!str) return str;
  return str.replace(/\{\{(\w+)\}\}/g, (m, key) =>
    key in slots ? String(slots[key]) : m
  );
}

function interpolateString(input, resolver) {
  if (typeof input !== "string") return input;
  let str = input;
  const varRe = /\$\{([^}]+)\}/g;

  for (let pass = 0; pass < 5; pass++) {
    let changed = false;
    str = str.replace(varRe, (m, expr) => {
      const key = expr.trim();
      const val = resolver(key);
      if (val === undefined || val === null) {
        return `[[MISSING: ${key}]]`;
      }
      changed = true;
      return String(val);
    });
    if (!changed) break;
  }
  return str;
}

function makeResolver({
  pageName,
  componentName,
  pageContent,
  templateContent,
  globalContent,
  dynamicContextVars,
  extra,
}) {
  const flat = mergeShallow(dynamicContextVars, extra);

  return (rawKey) => {
    const resolvedKey = /\$\{/.test(rawKey)
      ? interpolateString(rawKey, (k) => flat[k])
      : rawKey;

    if (resolvedKey in flat) return flat[resolvedKey];

    const path1 = [pageName, componentName, resolvedKey].filter(Boolean);
    const v1 =
      deepGet(pageContent, path1) ??
      deepGet(templateContent, path1) ??
      deepGet(globalContent, path1);
    if (v1 !== undefined) return v1;

    const path2 = [pageName, resolvedKey].filter(Boolean);
    const v2 =
      deepGet(pageContent, path2) ??
      deepGet(templateContent, path2) ??
      deepGet(globalContent, path2);
    if (v2 !== undefined) return v2;

    const path3 = [componentName, resolvedKey].filter(Boolean);
    const v3 =
      deepGet(pageContent, path3) ??
      deepGet(templateContent, path3) ??
      deepGet(globalContent, path3);
    if (v3 !== undefined) return v3;

    const v4 =
      (pageContent && pageContent[resolvedKey]) ??
      (templateContent && templateContent[resolvedKey]) ??
      (globalContent && globalContent[resolvedKey]);
    if (v4 !== undefined) return v4;

    return undefined;
  };
}

function renderComponent({
  pageName,
  component,
  pageContent,
  templateContent,
  globalContent,
  dynamicContextVars,
  extra = {},
  repeaterDataForSlots = {},
}) {
  const componentName = component.name;

  const resolver = makeResolver({
    pageName,
    componentName,
    pageContent,
    templateContent,
    globalContent,
    dynamicContextVars,
    extra,
  });

  const placeholderBag = {};
  if (Array.isArray(component.placeholders)) {
    for (const ph of component.placeholders) {
      placeholderBag[ph] = resolver(ph);
    }
  }

  let html = interpolateString(component.html_content || "", (k) => {
    if (k in placeholderBag && placeholderBag[k] != null)
      return placeholderBag[k];
    return resolver(k);
  });

  html = replaceSlots(html, repeaterDataForSlots);

  const css = interpolateString(component.css_content || "", resolver);
  const js = interpolateString(component.js_content || "", resolver);

  return {
    ...component,
    rendered_html: html,
    rendered_css: css,
    rendered_js: js,
  };
}

function renderRepeaterSlot({
  pageName,
  containerComponent,
  repeatedComponent,
  rows,
  slotName,
  pageContent,
  templateContent,
  globalContent,
  dynamicContextVars,
}) {
  if (!Array.isArray(rows) || !repeatedComponent) return { slotName, html: "" };

  const pieces = [];
  for (const row of rows) {
    const rendered = renderComponent({
      pageName,
      component: repeatedComponent,
      pageContent,
      templateContent,
      globalContent,
      dynamicContextVars,
      extra: row,
    });
    pieces.push(rendered.rendered_html);
  }
  return { slotName, html: pieces.join("\n") };
}

function renderPage({
  page,
  pageName,
  pageContent,
  templateContent,
  globalContent,
  dynamicContextVars,
}) {
  const out = JSON.parse(JSON.stringify(page));
  const components = Array.isArray(page.components) ? page.components : [];

  out.rendered_components = [];
  out.__css = [];
  out.__js = [];

  for (const comp of components) {
    let slots = {};
    if (
      comp.repeator === "repeat" &&
      comp.repeatedComponent &&
      comp.repeatedComponentName
    ) {
      const slotKey = Array.isArray(comp.repeatedComponentName)
        ? comp.repeatedComponentName[0]
        : comp.repeatedComponentName;

      const resolver = makeResolver({
        pageName,
        componentName: comp.name,
        pageContent,
        templateContent,
        globalContent,
        dynamicContextVars,
        extra: {},
      });

      const rows =
        resolver(slotKey) || resolver(`${comp.name}.${slotKey}`) || [];

      const { html } = renderRepeaterSlot({
        pageName,
        containerComponent: comp,
        repeatedComponent: comp.repeatedComponent,
        rows: Array.isArray(rows) ? rows : [],
        slotName: slotKey,
        pageContent,
        templateContent,
        globalContent,
        dynamicContextVars,
      });
      slots[slotKey] = html;
    }

    const rendered = renderComponent({
      pageName,
      component: comp,
      pageContent,
      templateContent,
      globalContent,
      dynamicContextVars,
      repeaterDataForSlots: slots,
    });

    out.rendered_components.push({
      id: comp.id,
      name: comp.name,
      order: comp.order ?? 0,
      rendered_html: rendered.rendered_html,
    });

    if (rendered.rendered_css) out.__css.push(rendered.rendered_css);
    if (rendered.rendered_js) out.__js.push(rendered.rendered_js);
  }

  out.rendered_full_page_content = interpolateString(
    page.full_page_content || "",
    makeResolver({
      pageName,
      componentName: "",
      pageContent,
      templateContent,
      globalContent,
      dynamicContextVars,
      extra: {},
    })
  );

  return out;
}

function buildPreview({ templateData, dynamicData, pageContentOverride }) {
  const previewPage = [];

  if (!templateData || !Array.isArray(templateData.pages)) return previewPage;

  const globalContent = templateData.contentJSON || {};
  const templateContent = templateData.templateContent || {};

  for (const originalPage of templateData.pages) {
    const page = JSON.parse(JSON.stringify(originalPage));
    const pageName = page.name || page.slug || "page";

    const pageContent =
      pageContentOverride && pageContentOverride[pageName]
        ? pageContentOverride
        : globalContent;

    if (page.type === "static") {
      const renderedPage = renderPage({
        page,
        pageName,
        pageContent,
        templateContent,
        globalContent,
        dynamicContextVars: {},
      });
      previewPage.push(renderedPage);
      continue;
    }

    if (page.type === "dynamic") {
      const dynById = dynamicData && dynamicData[page.id];
      const dynByName = dynamicData && dynamicData[page.name];
      const dynCfg = dynById || dynByName;

      if (!dynCfg) {
        const renderedPage = renderPage({
          page,
          pageName,
          pageContent,
          templateContent,
          globalContent,
          dynamicContextVars: {},
        });
        previewPage.push(renderedPage);
        continue;
      }

      const sections = Array.isArray(dynCfg.sections)
        ? dynCfg.sections
        : dynCfg.sections || {};
      const sectionArrays = Array.isArray(sections)
        ? sections
        : Object.values(sections);

      for (const arr of sectionArrays) {
        if (!Array.isArray(arr)) continue;
        for (const slugObj of arr) {
          const dynamicContextVars = { slug: slugObj.slug, name: slugObj.name };
          const dynamicUrl = [page.url, slugObj.slug]
            .join("/")
            .replace(/\/{2,}/g, "/");
          const cloned = JSON.parse(JSON.stringify(page));
          cloned.url = dynamicUrl;

          const renderedPage = renderPage({
            page: cloned,
            pageName,
            pageContent,
            templateContent,
            globalContent,
            dynamicContextVars,
          });
          previewPage.push(renderedPage);
        }
      }
      continue;
    }

    const renderedPage = renderPage({
      page,
      pageName,
      pageContent,
      templateContent,
      globalContent,
      dynamicContextVars: {},
    });
    previewPage.push(renderedPage);
  }

  return previewPage;
}

/***********************
 * React Component
 ***********************/

const templateData2 = {
  name: "test-home",
  full_template_content: "....",
  pages: [
    {
      name: "testing-home",
      slug: "home",
      full_page_content: "....",
      type: "static",
      url: "/home",
      order: 0,
      showNavbar: true,
      components: [
        {
          name: "hero_section_home_page",
          category_id: "cat2",
          html_content:
            '<div data-component="hero_section_home_page"><h1>${hero_section_title}</h1><p>${hero_section_description}</p><p>${contact_number}</p><p>${contact_number_button_label}</p><a href="${quote_url}">${get_quote_button_text}</a></div>',
          css_content: "",
          js_content: "",
          placeholders: [
            "hero_section_title",
            "hero_section_description",
            "contact_number",
            "hero_section_extra_text",
            "contact_number_button_label",
            "quote_url",
            "get_quote_button_text",
          ],
          version: 1,
          status: "draft",
          id: "hero-1",
          order: 0,
          repeator: "single",
        },
        {
          name: "bus_grid_list",
          category_id: "cat2",
          html_content:
            '<div data-component="bus_grid_list"><h2>${buses_list_heading}</h2><div class="grid">{{busesList}}</div></div>',
          css_content: "",
          js_content: "",
          placeholders: ["buses_list_heading"],
          version: 1,
          status: "draft",
          id: "bus-container",
          order: 1,
          repeator: "repeat",
          repeatedComponentName: ["busesList"],
          repeatedComponent: {
            name: "bud_grid_card",
            html_content:
              '<div class="card"><img src="${img-busImage}"><h3>${bus_title}</h3><a href="${view_Bus_Url}">${bus_view_button_text}</a><a href="${quote_url}">${get_quote_button_text}</a></div>',
            placeholders: [
              "bus_price_range",
              "img-busImage",
              "img-busInterior",
              "view_Bus_Url",
              "bus_title",
              "bus_view_button_text",
              "quote_url",
              "get_quote_button_text",
            ],
            id: "bus-card",
          },
        },
      ],
    },
    // Example dynamic page config (will use dynamic_Data["city_page"])
    {
      name: "city_page",
      slug: "city",
      type: "dynamic",
      url: "/city",
      components: [
        {
          name: "hero_section_home_page",
          html_content:
            "<div><h1>Charter bus Rental ${slug}, ${${slug}_state}</h1><p>${hacken_hero_description}</p></div>",
          placeholders: ["hacken_hero_description"],
          id: "city-hero",
        },
      ],
    },
  ],

  templateContent: {
    city_page: {
      hero_section_home_page: {
        hero_section_title: "Charter bus Rental ${slug}, ${${slug}_state}",
        // template-level default description (if page content missing)
        hacken_hero_description: "Default template description for ${slug}.",
      },
    },
  },

  version: 1,
  status: "draft",
  id: "memc3fnm90sev4blxbw",
  contentJSON: {
    city_page: {
      hero_section_home_page: {
        ahmedabad_state: "Gujrat",
        surat_state: "Gujrat",
        hacken_hero_description: "Book a Clifton Bus Rental in Minutes!",
      },
    },
    "testing-home": {
      hero_section_home_page: {
        hero_section_title: "Welcome to Our Static Home",
        hero_section_description: "Static page description goes here.",
        contact_number: "+91-9999999999",
        contact_number_button_label: "Call Us",
        quote_url: "/quote",
        get_quote_button_text: "Get Quote",
      },
      bus_grid_list: {
        buses_list_heading: "Popular Buses",
        // Provide repeater rows for slot "busesList"
        busesList: [
          {
            "img-busImage": "/img/bus1.jpg",
            bus_title: "Mini Coach 20 Seater",
            view_Bus_Url: "/bus/mini-coach",
            bus_view_button_text: "View",
            quote_url: "/quote?bus=mini",
            get_quote_button_text: "Get Quote",
          },
          {
            "img-busImage": "/img/bus2.jpg",
            bus_title: "Luxury Coach 45 Seater",
            view_Bus_Url: "/bus/luxury-coach",
            bus_view_button_text: "View",
            quote_url: "/quote?bus=lux",
            get_quote_button_text: "Get Quote",
          },
        ],
      },
    },
  },
};

const dynamic_Data = {
  city_page: {
    siteMapLabel: "City Page",
    navigationType: 1,
    ColumnCount: 1,
    sections: {
      city: [
        { id: 1, name: "Ahmedabad", slug: "ahmedabad" },
        { id: 2, name: "Surat", slug: "surat" },
      ],
    },
  },
};
export default function TemplatePreview({
  templateData = templateData2,
  dynamicData = dynamic_Data,
}) {
  const preview = useMemo(
    () => buildPreview({ templateData, dynamicData }),
    [templateData, dynamicData]
  );

  const [activeIdx, setActiveIdx] = useState(0);
  const activePage = preview[activeIdx];

  // Inject page-level CSS (concat component CSS strings)
  useEffect(() => {
    const styleEl = document.getElementById("template-preview-style");
    const css = (activePage?.__css || []).join("\n\n");
    if (styleEl) styleEl.textContent = css;
    else {
      const s = document.createElement("style");
      s.id = "template-preview-style";
      s.textContent = css;
      document.head.appendChild(s);
    }
    // JS execution is intentionally NOT auto-run for safety
  }, [activePage]);

  return (
    <div className="tw-w-full tw-h-full tw-flex tw-gap-4 tw-p-4">
      {/* Sidebar */}
      <aside className="tw-w-64 tw-shrink-0 tw-border tw-rounded-2xl tw-p-3 tw-overflow-auto">
        <h2 className="tw-text-lg tw-font-semibold tw-mb-2">Pages</h2>
        <ul className="tw-space-y-1">
          {preview.map((p, i) => (
            <li key={p.url || i}>
              <button
                className={`tw-w-full tw-text-left tw-px-3 tw-py-2 tw-rounded-xl ${
                  i === activeIdx ? "tw-bg-gray-200" : "hover:tw-bg-gray-100"
                }`}
                onClick={() => setActiveIdx(i)}
              >
                <div className="tw-text-sm tw-font-medium">
                  {p.name || p.slug || `Page ${i + 1}`}
                </div>
                <div className="tw-text-xs tw-text-gray-600">
                  {p.url || "/"}
                </div>
              </button>
            </li>
          ))}
        </ul>
      </aside>

      {/* Canvas */}
      <main className="tw-flex-1 tw-border tw-rounded-2xl tw-p-4 tw-overflow-auto">
        {!activePage ? (
          <div className="tw-text-gray-500">No page to render.</div>
        ) : (
          <div className="tw-space-y-6">
            <div className="tw-flex tw-items-center tw-justify-between">
              <div>
                <h1 className="tw-text-xl tw-font-semibold">
                  {activePage.name || activePage.slug}
                </h1>
                <div className="tw-text-sm tw-text-gray-600">
                  {activePage.url}
                </div>
              </div>
              <div className="tw-text-xs tw-text-gray-500">
                Type: {activePage.type || "static"}
              </div>
            </div>

            {activePage.rendered_full_page_content && (
              <section className="tw-border tw-rounded-xl tw-p-3">
                <div className="tw-text-xs tw-font-mono tw-text-gray-500 tw-mb-2">
                  full_page_content
                </div>
                <div
                  dangerouslySetInnerHTML={{
                    __html: activePage.rendered_full_page_content,
                  }}
                />
              </section>
            )}

            {activePage.rendered_components
              .slice()
              .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
              .map((c) => (
                <section key={c.id} className="tw-border tw-rounded-xl tw-p-3">
                  <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
                    <div className="tw-text-sm tw-font-semibold">{c.name}</div>
                    <div className="tw-text-xs tw-text-gray-500">
                      order {c.order ?? 0}
                    </div>
                  </div>
                  <div dangerouslySetInnerHTML={{ __html: c.rendered_html }} />
                </section>
              ))}
          </div>
        )}
      </main>
    </div>
  );
}
